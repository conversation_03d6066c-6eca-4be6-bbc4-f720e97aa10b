<template>
  <div class="home">
    <!-- 头部横幅 -->
    <section class="hero">
      <div class="hero-content">
        <h1 class="hero-title">《人工智能概论与应用》</h1>
        <p class="hero-subtitle">数字化教材平台</p>
        <p class="hero-description">
          基于GoodLab团队编写的《人工智能概论与应用》教材，
          通过现代化的Web技术为学生提供更加生动、有趣的人工智能学习体验。
        </p>
        <div class="hero-actions">
          <router-link to="/chapters" class="btn btn-primary">开始学习</router-link>
          <router-link to="/about" class="btn btn-secondary">了解更多</router-link>
        </div>
      </div>
    </section>

    <!-- 特色介绍 -->
    <section class="features">
      <div class="container">
        <h2 class="section-title">平台特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">📚</div>
            <h3>权威内容</h3>
            <p>基于专业教材，内容权威可靠，涵盖人工智能完整知识体系</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">💡</div>
            <h3>交互学习</h3>
            <p>比传统PDF更丰富的交互体验，让学习变得更加生动有趣</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h3>多端适配</h3>
            <p>支持桌面端、平板和移动端访问，随时随地学习</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔄</div>
            <h3>实时更新</h3>
            <p>内容可以实时更新和维护，始终保持最新的知识内容</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 章节概览 -->
    <section class="chapters-preview">
      <div class="container">
        <h2 class="section-title">章节概览</h2>
        <div class="chapters-grid" v-if="chapters.length > 0">
          <div 
            v-for="chapter in chapters" 
            :key="chapter.id" 
            class="chapter-card"
            @click="goToChapter(chapter.id)"
          >
            <div class="chapter-number">{{ chapter.chapterNumber === '0' ? '续章' : `第${chapter.chapterNumber}章` }}</div>
            <h3 class="chapter-title">{{ chapter.title }}</h3>
            <p class="chapter-summary">{{ chapter.summary }}</p>
          </div>
        </div>
        <div v-else class="loading">
          <p>正在加载章节信息...</p>
        </div>
        <div class="text-center">
          <router-link to="/chapters" class="btn btn-outline">查看所有章节</router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { chapterApi } from '../services/api'

export default {
  name: 'Home',
  data() {
    return {
      chapters: []
    }
  },
  async mounted() {
    await this.loadChapters()
  },
  methods: {
    async loadChapters() {
      try {
        const chapters = await chapterApi.getChapterOverview()
        this.chapters = chapters || []
        console.log('Home页面加载章节成功:', this.chapters)
      } catch (error) {
        console.error('加载章节失败:', error)
        this.chapters = []
      }
    },
    goToChapter(id) {
      this.$router.push(`/chapters/${id}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
}

.hero {
  background: linear-gradient(135deg, $primary-color 0%, $primary-gradient-end 100%);
  color: $text-color;
  padding: 100px 20px 60px 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
}
.hero::after {
  content: '';
  position: absolute;
  left: 50%; top: 100%;
  transform: translate(-50%, -50%);
  width: 120vw; height: 400px;
  background: radial-gradient(ellipse at center, $accent-color 0%, transparent 80%);
  opacity: 0.18;
  z-index: 0;
}
.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}
.hero-title {
  font-size: 3.2rem;
  font-weight: 800;
  margin-bottom: 1.2rem;
  letter-spacing: 2px;
}
.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: $accent-color;
  font-weight: 700;
  letter-spacing: 1px;
}
.hero-description {
  font-size: 1.15rem;
  line-height: 1.7;
  margin-bottom: 2.2rem;
  color: $text-secondary-color;
  opacity: 0.92;
}
.hero-actions {
  display: flex;
  gap: 1.2rem;
  justify-content: center;
  flex-wrap: wrap;
}
.btn-primary {
  background: $accent-color;
  color: $primary-color;
  border: none;
  border-radius: $border-radius * 1.2;
  font-weight: 700;
  font-size: 1.1rem;
  padding: 14px 36px;
  box-shadow: 0 4px 24px rgba(176,179,184,0.18);
  transition: all 0.25s;
}
.btn-primary:hover {
  background: $accent-color-light;
  color: $primary-hover-color;
  box-shadow: 0 8px 32px $accent-color;
  transform: translateY(-2px) scale(1.05);
}
.btn-secondary {
  background: transparent;
  color: $accent-color;
  border: 2px solid $accent-color;
  border-radius: $border-radius * 1.2;
  font-weight: 700;
  font-size: 1.1rem;
  padding: 14px 36px;
  transition: all 0.25s;
}
.btn-secondary:hover {
  background: $accent-color;
  color: $primary-color;
}

.features {
  padding: 90px 20px 50px 20px;
  background: $secondary-color;
}
.section-title {
  text-align: center;
  font-size: 2.6rem;
  margin-bottom: 3.5rem;
  color: $text-color;
  font-weight: 800;
  letter-spacing: 2px;
}
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2.8rem;
}
.feature-card {
  background: rgba(36, 38, 44, 0.85);
  border-radius: $border-radius * 1.5;
  box-shadow: 0 8px 32px rgba(24, 25, 26, 0.18);
  padding: 2.8rem 2rem 2.2rem 2rem;
  text-align: left;
  border: 1.5px solid rgba(176,179,184,0.08);
  position: relative;
  transition: box-shadow 0.18s, background 0.18s, border 0.18s;
  color: $text-secondary-color;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  backdrop-filter: blur(2px);

  .feature-icon {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    background: $accent-color;
    color: $primary-color;
    border-radius: 50%;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 12px rgba(176,179,184,0.13);
  }
  h3 {
    color: $text-color;
    font-size: 1.18rem;
    font-weight: 700;
    margin-bottom: 0.6rem;
    letter-spacing: 1px;
  }
  p {
    color: $text-secondary-color;
    font-size: 1.05rem;
    line-height: 1.8;
    margin-bottom: 0;
  }
}
.feature-card:hover {
  box-shadow: 0 8px 24px $accent-color;
  background: rgba(36, 38, 44, 0.97);
  border: 1.5px solid $accent-color;
}

.chapters-preview {
  padding: 90px 20px 50px 20px;
  background: $secondary-color;
}
.chapters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}
.chapter-card {
  background: rgba(36, 38, 44, 0.85);
  border-radius: $border-radius * 1.5;
  padding: 1.8rem;
  box-shadow: $box-shadow;
  cursor: pointer;
  transition: box-shadow 0.18s, background 0.18s, border 0.18s;
  border: 1.5px solid rgba(176,179,184,0.08);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.chapter-card:hover {
  box-shadow: 0 8px 24px $accent-color;
  background: rgba(36, 38, 44, 0.97);
  border: 1.5px solid $accent-color;
}
.chapter-number {
  color: $accent-color;
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}
.chapter-title {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: $text-color;
  font-weight: 700;
}
.chapter-summary {
  color: $text-secondary-color;
  line-height: 1.6;
  font-size: 1.02rem;
}
.loading {
  text-align: center;
  padding: 2rem;
  color: $text-secondary-color;
}
.text-center {
  text-align: center;
}

@media (max-width: 900px) {
  .hero-title { font-size: 2rem; }
  .hero-content { padding: 0 0.5rem; }
  .features-grid, .chapters-grid { grid-template-columns: 1fr; }
}
@media (max-width: 600px) {
  .hero { padding: 60px 8px 30px 8px; border-bottom-left-radius: 20px; border-bottom-right-radius: 20px; }
  .features, .chapters-preview { padding: 40px 8px 20px 8px; }
  .section-title { font-size: 1.5rem; margin-bottom: 1.5rem; }
  .feature-card, .chapter-card { padding: 1.2rem; border-radius: $border-radius; }
}
@media (hover: none) {
  .feature-card:hover, .chapter-card:hover {
    box-shadow: $box-shadow;
    background: rgba(36, 38, 44, 0.85);
    border: 1.5px solid rgba(176,179,184,0.08);
  }
}
</style>

\usepackage{amsmath}                                    % 排版数学公式
\usepackage{latexsym}
\usepackage{amsfonts}                                   %数学符号字体库宏包套件，它包含有：amsfonts、amssymb、eufrak 和 eucal 四个宏包。
\usepackage{amssymb}                                    % 定义AMS的数学符号命令
\usepackage{mathrsfs}                                   % 数学RSFS书写字体
\usepackage{bm}                                         % 数学黑体
\usepackage{graphicx}                                   % 支持插图,图形宏包graphics的扩展宏包
\usepackage{color,xcolor}                               % 支持彩色
\usepackage{amscd}
\usepackage[linesnumbered,ruled,vlined]{algorithm2e}
\usepackage{diagbox}
\usepackage{minted}
\usepackage{listings}                                   %引入代码环境
\renewcommand{\lstlistingname}{程序清单}
\usepackage{titlesec}                                   %设置章节格式
\usepackage{enumerate}                                 	%更改enumerate环境格式
\usepackage{hyperref}

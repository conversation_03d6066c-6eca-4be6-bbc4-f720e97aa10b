# 服务器配置
server.port=8082
server.servlet.context-path=/

# 应用配置
spring.application.name=ai-textbook-server

# 编码配置
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 日志配置
logging.level.com.goodlab=DEBUG
logging.level.org.springframework=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# JSON配置
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

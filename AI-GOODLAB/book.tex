\documentclass[openany,12pt,UTF8]{ctexbook}
\usepackage[font=small, labelfont=bf]{caption}
\usepackage{booktabs}      % 更美观的表格线
\usepackage{array}         % 提供更多表格选项
\usepackage{longtable}
\usepackage{booktabs}
\usepackage{subcaption}
\usepackage[most]{tcolorbox}
\usepackage{float}
\usepackage{ragged2e} % 提供更好的文本对齐
\renewcommand{\baselinestretch}{1.3}  % 1.5 倍行距
\usepackage[a4paper, left=1in, right=1in, top=1in, bottom=1in]{geometry}
\usepackage{appendix}
\include{package.tex}
\usepackage{makeidx}
 \makeindex 
\usepackage{enumitem}
\begin{document}
\lstset{keepspaces=true,showstringspaces=false}
\frontmatter

\include{title.tex}

% \vspace*{\fill}
%     \begin{center}
%         \textit{\large 谨以此书献给我的家人。}
%     \end{center}
% \vspace*{\fill}

\include{preface.tex}

\tableofcontents

\mainmatter
\include{chapter_0.tex}
\include{chapter_1.tex}
\include{chapter_2.tex}
\include{chapter_3.tex}
\include{chapter_4.tex}
\include{chapter_5.tex}
\include{chapter_6.tex}
\include{chapter_7.tex}

\appendix \include{appendix.tex}
% \include{attachment.tex}

\end{document}

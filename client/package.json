{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@codemirror/lang-python": "^6.2.1", "@codemirror/theme-one-dark": "^6.1.3", "@element-plus/icons-vue": "^2.3.1", "@types/three": "^0.178.1", "axios": "^1.10.0", "chart.js": "^4.5.0", "codemirror": "^6.0.2", "echarts": "^5.6.0", "element-plus": "^2.10.4", "gsap": "^3.13.0", "plotly.js": "^3.0.3", "plotly.js-dist-min": "^3.0.3", "three": "^0.178.0", "uuid": "^11.1.0", "vue": "^3.5.17", "vue-chartjs": "^5.3.2", "vue-codemirror": "^6.1.1", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "sass-embedded": "^1.89.2", "vite": "^7.0.4"}}
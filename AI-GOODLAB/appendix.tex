\chapter{专业名词解释}

\begin{enumerate}

\item \textbf{图形处理单元}（Graphics Processing Unit, GPU）：一种专为并行计算设计的硬件设备，最初用于加速图形渲染任务，如游戏画面和3D建模。GPU拥有大量的小型计算核心，能够同时处理多个数据块的计算任务，这种特性使其在深度学习模型训练中发挥了重要作用，大幅提升了神经网络的计算速度和效率。

\item \textbf{中央处理单元}（Central Processing Unit, CPU）：计算机的核心处理器，主要负责执行程序中的各种逻辑和运算任务。CPU更擅长处理单一的复杂任务，类似于一位精通多种技能的工匠，但其核心数量较少，因此在处理需要大量并行计算的任务时效率较低。

\item \textbf{矩阵运算}：对二维数组（矩阵）进行的加法、乘法、转置等操作，在深度学习中广泛应用于神经网络的权重更新、激活函数计算等环节。这些运算本质上是线性代数的核心内容，支撑着模型的前向传播和反向传播过程。矩阵运算是神经网络计算的基础。前向传播过程中，模型需要对输入数据与权重矩阵进行乘法运算，再加上偏置项，从而得到各层的输出结果。而在反向传播中，模型通过链式求导法则对误差进行传播，并根据梯度更新每一层的权重和偏置参数。矩阵的乘法和转置操作在权重更新的过程中尤为重要，影响了模型的收敛速度和精度。
\item \textbf{非线性数据}：变量之间的关系无法用简单的直线表达的复杂数据类型。
\item \textbf{迭代学习}：通过反复调整模型和重新评估结果，逐步改进模型性能的过程。
\item \textbf{模型训练}：通过已有数据调整模型参数，使其能够识别数据模式并做出预测的过程，例如使用历史销售数据训练模型预测未来销量。
\item \textbf{验证与评估}：指利用独立的数据集来测试模型性能，以衡量模型在处理新数据时的准确性和鲁棒性。
\item \textbf{鲁棒性}：模型在面对噪声、异常数据或其他不可预见情况时，仍然能够保持稳定性能的能力。
\item \textbf{模型参数}：模型中需要学习的系数，这些系数反映了每个输入变量对输出结果的影响大小。
\item \textbf{异常值}：显著偏离其他数据点的值，可能由于测量误差或极端情况导致。在模型中，这些值会对结果产生较大影响，需加以处理。
\item \textbf{阈值}：分类决策中的关键参数，用于将概率值划分为不同类别。合适的阈值选择需综合考虑分类任务的性质和错误成本。
\item \textbf{Softmax回归}：一种扩展逻辑回归以支持多分类任务的方法。它使用Softmax函数将模型的输出映射到多个类别的概率分布上，从而实现对多个类别的预测。
\item \textbf{调和平均值}：一种统计学计算方法，强调小数值对整体结果的影响。公式为：\( F1 = 2 \times \frac{\text{精确率} \times \text{召回率}}{\text{精确率} + \text{召回率}} \)。它特别适用于评估需要在多个指标间找到平衡的场景。
\item \textbf{正则化}：一种机器学习技术，旨在限制模型的复杂度，避免过度拟合训练数据。常见的正则化方法包括L1正则化（Lasso）和L2正则化（Ridge），通过在损失函数中加入惩罚项减少模型对特定特征的依赖。
\item \textbf{数据规律}：数据中隐藏的模式或趋势，模型需要通过训练过程识别这些规律来提高预测能力。未能识别数据规律可能是由于模型过于简单或数据不足导致的。
\item \textbf{模型复杂度}：指模型的容量或表达能力，通常由参数的数量或模型结构的层次决定。较高的复杂度可以让模型更好地适应复杂的数据分布，但可能导致过拟合问题。
\item \textbf{超参数调整}：指在模型训练之前手动或自动选择适合的参数，这些参数不会通过训练过程更新，例如学习率、正则化系数和批量大小。超参数调整直接影响模型的收敛速度和性能。
\item \textbf{学习率}：是超参数之一，用于控制模型每次更新参数的步幅大小。较大的学习率可能导致模型跳过最优解，而较小的学习率则可能使训练过程过于缓慢。找到适当的学习率有助于平衡训练效率和准确性。
\item \textbf{正则化系数}：是超参数之一，用于控制正则化项对模型的约束力度。较大的正则化系数可以有效减少过拟合，但可能导致欠拟合。
\item \textbf{批量大小}：指每次训练过程中用于更新模型参数的数据样本数量。较大的批量大小可以提高计算效率，但可能影响模型的泛化能力；较小的批量大小通常需要更长的训练时间，但可能更有助于模型的泛化。
\item \textbf{网格搜索}：一种系统化的超参数调整方法，通过在预设的参数网格中逐一尝试每种组合，找到最优的超参数配置。
\item\textbf{错误分析}：通过检查模型预测错误的样本，分析模型性能问题的一种方法。它有助于发现模型设计或数据分布中的不足，从而为进一步优化提供指导。
\item \textbf{样本}：是指用于模型训练或评估的单个数据点，例如一个记录、一张图片或一段文本。样本的质量和多样性对模型的性能有直接影响。
\item \textbf{数据不平衡}：数据集中不同类别的样本数量分布不均，可能导致模型对样本数量较多的类别预测较好，而对样本较少的类别表现较差
\item \textbf{多层结构}：深度学习模型由多个隐藏层组成，每一层负责提取数据的不同特征，从简单到复杂逐步处理，从而解决复杂问题。例如，第一层可能提取图像的边缘信息，第二层识别形状，最后的层则判断具体的类别。
\item \textbf{神经网络}：一种仿生计算结构，模拟人脑的工作方式，由输入层、隐藏层和输出层组成。每一层通过权重和偏置连接，用于提取数据的特征和模式，最终形成预测或决策。
\item \textbf{传感器}：用于监测环境条件（如温湿度、光照强度等）的设备，能够实时采集数据，为分析和决策提供基础支持。
\item \textbf{卷积神经网络（CNN）}：一种特别适合图像处理的深度学习模型，通过提取图像的局部特征进行分类和识别。
\item \textbf{循环神经网络（RNN）}：一种擅长处理时间序列数据的深度学习模型，可捕捉数据间的上下文信息。
\item \textbf{空间结构}：指数据中存在的内在规律，例如图像的像素分布或声音的时间序列。卷积层通过捕捉这些规律来提取有用信息。
\item \textbf{滤波器}（Filter）：一种用于特征提取的小矩阵，也称为卷积核（Kernel）。它通过滑动窗口的方式与输入数据进行数学运算，提取局部特征，如图像的边缘、角点等。
\item \textbf{噪声}：数据中无关或随机的干扰，例如图像中的光照变化或语音中的背景噪音。池化层通过压缩数据，可以有效减少噪声对模型的影响。
\item \textbf{节点}：神经网络中的基本单元，它接收输入、执行计算并输出结果，类似于生物神经元的功能。
\item \textbf{过拟合}：指模型在训练数据上表现很好，但在新数据上效果较差的现象。通常可以通过正则化技术或增加数据量来缓解。
\item \textbf{Dropout}：一种正则化方法，通过随机丢弃部分节点，降低模型对特定特征的依赖，从而提高模型的泛化能力。
\item \textbf{物联网技术}（Internet of Things，IoT）：通过互联网将各种智能设备互联互通，实现远程控制、数据交换和智能化管理。
\item \textbf{Transformer架构}：一种深度学习模型，主要用于自然语言处理，它通过自注意力机制和并行处理能力，显著提高了序列数据的处理效率和效果。
\item \textbf{自然语言理解（Natural Language Understanding,NLU）}：旨在解析用户的语音或文本输入，理解其中的意图和实体。主要包括语义分析、句法分析、情感分析等。NLU技术通过识别用户意图（Intent Recognition）和提取关键实体（Entity Extraction），实现对用户需求的准确把握。
\item \textbf{自然语言生成（Natural Language Generation,NLG）}：根据理解到的意图和上下文信息，生成符合语法和语境的自然语言回应。NLG技术通过构建语言模型，确保生成的回应既准确又自然，提升用户的交互体验。
\item \textbf{非结构化数据}：指没有固定格式或预定义模型的数据，如文本、图像和视频，难以通过传统的数据库管理系统进行处理和分析。
\item \textbf{虚拟现实（Virtual Reality, VR）}：一种通过计算机技术创造沉浸式虚拟环境的技术，使用户能够与该环境进行交互。
\item \textbf{增强现实（Augmented Reality, AR）}：一种将虚拟信息叠加到现实世界中，以实现交互和信息增强的技术。
\item \textbf{数字人技术}是指利用计算机图形学、人工智能和虚拟现实等技术创建和模拟虚拟人类形象及其行为的领域。
\item \textbf{自适应巡航控制（Adaptive Cruise Control, ACC）}：一种自动驾驶辅助系统，能够根据前方车辆的速度自动调整自身车速，以保持安全的跟车距离。
\item \textbf{OTA（Over-The-Air）}：一种通过无线网络远程更新软件或固件的技术。OTA更新使得Autopilot系统能够不断优化和提升其性能，而无需车主手动进行更新。这种方式依赖于大规模的数据收集和深度学习，确保系统在各种驾驶场景中具备更好的适应性和可靠性。
\item \textbf{C-V2X (Cellular Vehicle-to-Everything)车联网技术}：一种基于蜂窝网络的车用无线通信技术，可实现车辆与周围环境，包括其他车辆、基础设施、行人以及网络之间的全方位通信。
\item \textbf{ 单核苷酸多态性（SNP）}：指在基因组中，单个核苷酸的序列因个体之间的遗传差异而出现的变异。
\item \textbf{组学}：通过高通量技术研究生物大分子（如基因、蛋白质、代谢物等）及其相互关系的学科，通常用于理解生物系统的整体功能和动态。
\item \textbf{图神经网络（Graph Neural Network，GNN）}是一种专门处理图结构数据的深度学习模型。它通过将图的节点及其邻居的信息进行聚合和更新，来学习节点的表示，并可用于分类、回归、链接预测等任务。GNN在社交网络、生物信息学、交通网络等领域得到广泛应用，能够有效地捕捉图中复杂的关系和结构信息。
\item \textbf{支持向量机（Support Vector Machine，SVM）}：一种强大的机器学习算法，广泛用于分类和回归任务。它的核心思想是通过找到一个最优的超平面，将不同类别的数据点分隔开。这个超平面是决策边界，旨在最大化邻近数据点（即支持向量）与边界之间的间隔（即“间隔最大化”）。
\item \textbf{随机森林（Random Forest，RF）}：一种集成学习方法，通过构建多个决策树并结合它们的预测结果来提高分类和回归的准确性与稳定性。
\item \textbf{认知行为疗法（Cognitive Behavioral Therapy, CBT）}：一种重要且广泛应用的心理治疗方法。它的核心理念在于，通过识别和改变个体内心的负面思维模式，帮助人们提升情绪状态和改善行为表现。这种治疗方法强调了思维方式与情感及行动之间的密切关系，使患者能够在认知上调整不健康的模式。

\end{enumerate}

# 《人工智能概论与应用》章节结构

## 续章：生活中的人工智能
- 人工智能在日常生活中的应用
- 清晨的闹铃和音乐播放
- 交通导航与信号灯

## 第一章：初识人工智能
- 人工智能的定义
- 智能的定义
  - 通识领域
  - 心理学领域
  - 人工智能研究领域
- 机器的智能
  - 图灵测试
  - 中文屋思想实验
- 人工智能发展历史
  - 第一个热潮（20世纪50-70年代）
  - 第二个热潮（20世纪80-90年代）
  - 第三个热潮（20世纪90年代末至今）
- 人工智能分类
  - 基于能力的分类（弱人工智能、强人工智能、超人工智能）
  - 基于学派的分类（符号学派、联结学派、贝叶斯学派、类推学派）
  - 基于关键技术的分类（计算机视觉、语音识别与自然语言处理、生成式人工智能）
- 人工智能的现在和未来

## 第二章：人工智能基础
- 人工智能、机器学习、深度学习的关系
- 机器学习
  - 机器学习的定义
  - 机器学习原理
  - 机器学习分类
  - 机器学习评估

## 第三章：人工智能核心技术
- 计算机视觉
  - 计算机视觉的奠基者
- 自然语言智能
- 生成式人工智能

## 第四章：人工智能的应用
- 智慧生活
  - 智能家居
  - 家庭自动化系统的工作原理
- 其他应用领域

## 第五章：人工智能的提示工程
- 提示工程简介
  - 定义与内涵
  - 发展历程
- 提示工程技巧
- 提示工程应用

## 第六章：第一个人工智能项目
- 人工智能的编程语言Python
  - Python简介与特点
  - 量身定制的编程语言
  - 学习曲线与开发效率
- 项目实践

## 第七章：人工智能的思考
- 人工智能的算法歧视问题
  - 算法歧视的概念和具体表现
  - 有损群体包容性的偏见
  - 有损群体公平性的偏见
  - 有损个体利益的偏见
- 伦理与社会影响

## 附录：专业名词解释
- 技术术语解释
- 算法与模型相关术语
- 数据处理与评估术语